package model

import (
	"database/sql"
	_ "github.com/mattn/go-sqlite3"

	"net-interceptor/parser"
)

func InitDB(path string) (*sql.DB, error) {
	db, err := sql.Open("sqlite3", path)
	if err != nil {
		return nil, err
	}
	schema := `CREATE TABLE IF NOT EXISTS packets (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
		src_ip TEXT,
		dst_ip TEXT,
		src_port INTEGER,
		dst_port INTEGER,
		payload TEXT
	);`
	_, err = db.Exec(schema)
	return db, err
}

func SavePacket(db *sql.DB, p *parser.Packet) error {
	_, err := db.Exec(`INSERT INTO packets (src_ip, dst_ip, src_port, dst_port, payload) VALUES (?, ?, ?, ?, ?)`,
		p.Src<PERSON>, p.Dst<PERSON>, p.<PERSON>c<PERSON><PERSON>, p.DstPort, p.Payload)
	return err
}
